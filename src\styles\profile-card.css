/**
 * 🎯 PROFILE CARD STYLES
 *
 * Estilos específicos para o cartão de perfil baseado no repositório de referência
 */

/* ===== PROFILE CARD ELEGANTE - EFEITOS SUTIS ===== */
/* Card Base com Glassmorphism */
.profile-card {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Sombra base elegante */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

.dark .profile-card {
  background: rgba(17, 24, 39, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* ===== EFEITO HOVER ELEGANTE - LEVITAÇÃO SUAVE ===== */
.profile-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(59, 130, 246, 0.2) inset, 0 0 20px rgba(59, 130, 246, 0.1);
  /* Glow sutil */
  border-color: rgba(59, 130, 246, 0.3);
}

.dark .profile-card:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(59, 130, 246, 0.3) inset, 0 0 20px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
}

/* ===== ANEL ANIMADO ELEGANTE ===== */
.profile-ring {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #3b82f6);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
  transition: all 0.3s ease;
}

.profile-ring:hover {
  animation: gradientShift 1.5s ease infinite;
  transform: scale(1.02);
  filter: brightness(1.1);
}

/* ===== KEYFRAMES SUAVES ===== */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* ===== EFEITO IMAGEM ELEGANTE ===== */
.profile-image-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.profile-image-hover:hover {
  transform: scale(1.05);
  filter: brightness(1.1) contrast(1.05);
}

/* ===== STATUS ONLINE ELEGANTE ===== */
.status-online {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.3);
  transition: all 0.3s ease;
}

.profile-card:hover .status-online {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.4);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

/* ===== DARK MODE SUPPORT ===== */
[data-theme="dark"] .profile-card {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .profile-card:hover {
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.4),
    0 25px 50px -12px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .profile-card {
    max-width: 400px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .profile-card {
    max-width: 320px;
    padding: 1.5rem 1rem;
    margin: 0 auto;
  }

  .profile-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

@media (max-width: 480px) {
  .profile-card {
    max-width: 280px;
    padding: 1rem;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.profile-card,
.profile-ring,
.profile-image-hover,
.status-online {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Contact Item Hover */
.contact-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.contact-item:hover {
  transform: translateX(4px);
  background: rgba(59, 130, 246, 0.05);
}

.dark .contact-item:hover {
  background: rgba(59, 130, 246, 0.1);
}

/* Badge Styles */
.ixdf-badge {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #93c5fd;
  transition: all 0.3s ease;
}

.dark .ixdf-badge {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0.1) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.ixdf-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* CTA Button in Card */
.card-cta {
  background: linear-gradient(135deg, #25D366 0%, #1ebe5d 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
  background: linear-gradient(135deg, #1ebe5d 0%, #25D366 100%);
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .profile-ring,
  .status-online,
  .profile-image-hover,
  .contact-item,
  .ixdf-badge,
  .card-cta {
    animation: none;
    transition: none;
  }

  .profile-card:hover,
  .profile-image-hover:hover,
  .contact-item:hover,
  .ixdf-badge:hover,
  .card-cta:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .profile-card {
    border: 2px solid currentColor;
    background: var(--color-background);
  }

  .profile-ring {
    background: currentColor;
  }

  .ixdf-badge {
    border: 2px solid currentColor;
    background: var(--color-background);
  }
}
