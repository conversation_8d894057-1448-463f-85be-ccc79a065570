/* ===== SISTEMA MODULAR DE BOTÕES - CSS ===== */
/* Organização modular seguindo a estrutura dos componentes */

/* Base Styles */
@import './base.css';

/* Navigation Styles */
@import './navigation.css';

/* System Styles */
@import './system.css';

/* Accessibility Styles */
@import './accessibility.css';

/* Feedback Styles */
@import './feedback.css';

/* Form Styles */
@import './forms.css';

/* Action Styles */
@import './actions.css';

/* Specialized Styles */
@import './specialized.css';

/* ===== DESIGN TOKENS PARA BOTÕES ===== */
:root {
  /* Button Design Tokens */
  --button-border-radius: 8px;
  --button-border-radius-sm: 6px;
  --button-border-radius-lg: 12px;

  /* Button Spacing */
  --button-padding-sm: 8px 12px;
  --button-padding-md: 12px 16px;
  --button-padding-lg: 16px 24px;

  /* Button Heights */
  --button-height-sm: 32px;
  --button-height-md: 40px;
  --button-height-lg: 48px;

  /* Button Transitions */
  --button-transition: all 0.2s cubic-bezier(0, 0, 0.2, 1);
  --button-transition-fast: all 0.15s cubic-bezier(0, 0, 0.2, 1);

  /* Button Shadows */
  --button-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --button-shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --button-shadow-active: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* ===== RESET GLOBAL PARA BOTÕES MODULARES ===== */
.button-base,
.button-cta,
.button-enhanced,
.button-mobile-menu,
.button-mobile-config,
.button-theme-toggle,
.button-sound-toggle,
.button-language-switcher,
.button-accessibility,
.button-feedback,
.button-form,
.button-submit,
.button-action,
.button-icon,
.button-back-to-top,
.button-expand,
.button-collapse,
.button-pagination {
  /* Reset */
  border: none;
  outline: none;
  background: none;
  padding: 0;
  margin: 0;

  /* Base Properties */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  font-family: system-ui, -apple-system, sans-serif;
  cursor: pointer;
  user-select: none;
  text-decoration: none;
  position: relative;
  overflow: hidden;

  /* Accessibility */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;

  /* Transitions */
  transition: var(--button-transition);
}

/* ===== ESTADOS GLOBAIS ===== */
.button-base:disabled,
.button-cta:disabled,
.button-enhanced:disabled,
.button-form:disabled,
.button-submit:disabled,
.button-action:disabled,
.button-icon:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
  transform: none !important;
  box-shadow: var(--button-shadow) !important;
}

.button-base:focus-visible,
.button-cta:focus-visible,
.button-enhanced:focus-visible,
.button-form:focus-visible,
.button-submit:focus-visible,
.button-action:focus-visible,
.button-icon:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(var(--color-primary-rgb), 0.2);
}

/* ===== LOADING STATE ===== */
.button-loading {
  pointer-events: none;
  position: relative;
}

.button-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: button-spin 1s linear infinite;
}

@keyframes button-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== RESPONSIVIDADE ===== */
@media (max-width: 768px) {
  .button-base,
  .button-cta,
  .button-enhanced,
  .button-form,
  .button-submit,
  .button-action {
    min-height: 44px; /* Touch target minimum */
  }
}

/* ===== DARK MODE ===== */
.dark .button-base,
.dark .button-cta,
.dark .button-enhanced,
.dark .button-form,
.dark .button-submit,
.dark .button-action,
.dark .button-icon {
  /* Dark mode adjustments will be handled in individual component styles */
}
