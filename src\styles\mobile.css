/**
 * 🎯 MOBILE OPTIMIZATIONS
 * 
 * Melhorias específicas para dispositivos móveis
 * Baseado nas melhores práticas de UX mobile
 */

/* ===== VIEWPORT E SCROLL ===== */
html {
  /* <PERSON><PERSON><PERSON> scroll em dispositivos móveis */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

body {
  /* Evitar zoom horizontal em mobile */
  overflow-x: hidden;
  /* Melhorar renderização de texto */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== TIPOGRAFIA MOBILE ===== */
@media (max-width: 768px) {
  h1 {
    font-size: 2rem;
    line-height: 1.2;
  }
  
  h2 {
    font-size: 1.75rem;
    line-height: 1.3;
  }
  
  h3 {
    font-size: 1.5rem;
    line-height: 1.3;
  }
  
  h4 {
    font-size: 1.25rem;
    line-height: 1.4;
  }
  
  p {
    font-size: 1rem;
    line-height: 1.6;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 1.75rem;
    line-height: 1.2;
  }
  
  h2 {
    font-size: 1.5rem;
    line-height: 1.3;
  }
  
  h3 {
    font-size: 1.25rem;
    line-height: 1.3;
  }
  
  h4 {
    font-size: 1.125rem;
    line-height: 1.4;
  }
  
  p {
    font-size: 0.9rem;
    line-height: 1.6;
  }
}

/* ===== BOTÕES MOBILE ===== */
@media (max-width: 768px) {
  button,
  .btn {
    min-height: 48px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 8px;
  }
  
  .btn-sm {
    min-height: 40px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .btn-lg {
    min-height: 56px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
  }
}

/* ===== FORMULÁRIOS MOBILE ===== */
@media (max-width: 768px) {
  input,
  textarea,
  select {
    font-size: 16px; /* Evita zoom no iOS */
    min-height: 48px;
    padding: 0.75rem 1rem;
    border-radius: 8px;
  }
  
  textarea {
    min-height: 120px;
    resize: vertical;
  }
  
  /* Labels maiores para melhor legibilidade */
  label {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
  }
}

/* ===== NAVEGAÇÃO MOBILE ===== */
@media (max-width: 768px) {
  nav a {
    min-height: 44px;
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
  }
  
  /* Menu mobile */
  .mobile-menu {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    width: 280px;
    background: var(--color-surface);
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }
  
  .mobile-menu.open {
    transform: translateX(0);
  }
  
  .mobile-menu-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }
  
  .mobile-menu-overlay.open {
    opacity: 1;
    visibility: visible;
  }
}

/* ===== CARDS MOBILE ===== */
@media (max-width: 768px) {
  .card {
    margin-bottom: 1rem;
    border-radius: 12px;
    padding: 1.5rem;
  }
  
  .card-header {
    padding-bottom: 1rem;
    margin-bottom: 1rem;
  }
  
  .card-title {
    font-size: 1.25rem;
    line-height: 1.3;
  }
  
  .card-content {
    font-size: 0.9rem;
    line-height: 1.6;
  }
}

/* ===== IMAGENS RESPONSIVAS ===== */
@media (max-width: 768px) {
  img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
  }
  
  .hero-image {
    max-height: 300px;
    object-fit: cover;
  }
  
  .card-image {
    max-height: 200px;
    object-fit: cover;
  }
}

/* ===== ESPAÇAMENTOS MOBILE ===== */
@media (max-width: 768px) {
  .section {
    padding: 2rem 1rem;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .gap-4 {
    gap: 1rem;
  }
  
  .gap-6 {
    gap: 1.5rem;
  }
  
  .gap-8 {
    gap: 2rem;
  }
}

@media (max-width: 480px) {
  .section {
    padding: 1.5rem 0.75rem;
  }
  
  .container {
    padding: 0 0.75rem;
  }
  
  .gap-4 {
    gap: 0.75rem;
  }
  
  .gap-6 {
    gap: 1rem;
  }
  
  .gap-8 {
    gap: 1.5rem;
  }
}

/* ===== PERFORMANCE MOBILE ===== */
@media (max-width: 768px) {
  /* Reduzir animações em dispositivos móveis */
  * {
    animation-duration: 0.3s !important;
    transition-duration: 0.3s !important;
  }
  
  /* Otimizar scroll */
  .scroll-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}

/* ===== ACESSIBILIDADE MOBILE ===== */
@media (max-width: 768px) {
  /* Aumentar área de toque */
  a, button, input, textarea, select {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Melhorar contraste em telas pequenas */
  .text-muted {
    color: var(--color-text-secondary);
    opacity: 0.8;
  }
  
  /* Focus visível em mobile */
  *:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

/* ===== ORIENTAÇÃO LANDSCAPE ===== */
@media (max-width: 768px) and (orientation: landscape) {
  .section {
    padding: 1rem 0;
  }
  
  h1 {
    font-size: 1.5rem;
  }
  
  h2 {
    font-size: 1.25rem;
  }
}
