/**
 * 🎯 PROFILE COMPONENT
 *
 * Estilos para o componente Profile
 * Design responsivo com foco em mobile-first
 */

/* ===== CONTAINER PRINCIPAL ===== */
.profile {
  text-align: center;
  padding: var(--space-xl) 0;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== RESPONSIVIDADE MOBILE ===== */
@media (max-width: 768px) {
  .profile {
    padding: var(--space-lg) 0;
    min-height: 90vh;
  }
}

@media (max-width: 480px) {
  .profile {
    padding: var(--space-md) 0;
    min-height: 85vh;
  }
}

/* ===== MODO ESCURO ===== */
[data-theme="dark"] .profile {
  background: transparent;
}

/* ===== ACESSIBILIDADE ===== */
@media (prefers-reduced-motion: reduce) {
  .profile {
    transition: none;
  }
}
