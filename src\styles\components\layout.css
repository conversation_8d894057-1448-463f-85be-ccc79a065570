/**
 * 🎯 LAYOUT COMPONENT
 *
 * Estilos para layout geral
 * Design responsivo mobile-first
 */

/* ===== LAYOUT PRINCIPAL ===== */
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ===== CONTAINERS RESPONSIVOS ===== */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

/* ===== SEÇÕES RESPONSIVAS ===== */
.section {
  padding: 3rem 0;
}

.section-sm {
  padding: 2rem 0;
}

.section-lg {
  padding: 4rem 0;
}

/* ===== RESPONSIVIDADE MOBILE ===== */
@media (max-width: 768px) {
  .container {
    padding: 0 0.75rem;
  }

  .section {
    padding: 2rem 0;
  }

  .section-lg {
    padding: 3rem 0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }

  .section {
    padding: 1.5rem 0;
  }

  .section-sm {
    padding: 1rem 0;
  }

  .section-lg {
    padding: 2rem 0;
  }
}

/* ===== GRID RESPONSIVO ===== */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-1 {
  grid-template-columns: 1fr;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

@media (max-width: 768px) {
  .grid-cols-2,
  .grid-cols-3 {
    grid-template-columns: 1fr;
  }

  .grid {
    gap: 1rem;
  }
}

/* ===== FLEXBOX UTILITIES ===== */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

/* ===== SPACING MOBILE ===== */
@media (max-width: 480px) {
  .space-y-4 > * + * {
    margin-top: 1rem;
  }

  .space-y-6 > * + * {
    margin-top: 1.5rem;
  }

  .space-y-8 > * + * {
    margin-top: 2rem;
  }
}
