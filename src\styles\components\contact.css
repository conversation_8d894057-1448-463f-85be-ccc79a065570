/**
 * 🎯 CONTACT COMPONENT
 *
 * Estilos para o componente Contact
 * Design responsivo com foco em mobile-first
 */

/* ===== CONTAINER PRINCIPAL ===== */
.contact {
  padding: var(--space-xl);
}

/* ===== RESPONSIVIDADE MOBILE ===== */
@media (max-width: 768px) {
  .contact {
    padding: var(--space-lg) var(--space-md);
  }
}

@media (max-width: 480px) {
  .contact {
    padding: var(--space-md) var(--space-sm);
  }

  /* Melhorar campos de formulário em mobile */
  .contact input,
  .contact textarea {
    font-size: 16px; /* Evita zoom no iOS */
    padding: 0.875rem 1rem;
  }

  .contact textarea {
    min-height: 120px;
    resize: vertical;
  }

  /* Botões maiores em mobile */
  .contact button {
    min-height: 48px;
    font-size: 1rem;
    padding: 0.875rem 1.5rem;
  }
}

/* ===== MODO ESCURO ===== */
[data-theme="dark"] .contact {
  background: transparent;
}

/* ===== ACESSIBILIDADE ===== */
@media (prefers-reduced-motion: reduce) {
  .contact {
    transition: none;
  }
}

/* ===== MELHORIAS PARA TOUCH ===== */
@media (pointer: coarse) {
  .contact input,
  .contact textarea,
  .contact button {
    min-height: 44px; /* Tamanho mínimo recomendado para touch */
  }
}
